package org.thingsboard.server.controller.alarm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.alarmV2.AlarmCenterService;
import org.thingsboard.server.dao.model.request.AlarmCenterListRequest;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.model.sql.input.InputWuliao;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报警中心
 */
@RestController
@RequestMapping("api/alarmV2/alarmCenter")
public class AlarmCenterController extends BaseController {

    @Autowired
    private AlarmCenterService alarmCenterService;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private StationService stationService;

    /**
     * 告警列表
     * @param request
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("list")
    public IstarResponse findList(AlarmCenterListRequest request) throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.findList(request, getTenantId()));
    }

    /**
     * 告警到处，全量数据
     * @param request
     * @param response
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("list/export")
    public IstarResponse listExport(AlarmCenterListRequest request, HttpServletResponse response) throws ThingsboardException {
        request.setPage(1);
        request.setSize(Integer.MAX_VALUE);
        PageData<AlarmCenter> pageData = alarmCenterService.findList(request, getTenantId());

        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("stationName", "站点名称");
        headMap.put("attrName", "报警项");
        headMap.put("title", "报警标题");
        headMap.put("time", "首次报警时间");
        headMap.put("alarmLevel", "报警等级");
        headMap.put("alarmInfo", "报警描述");
        String title = "报警列表";

        // TODO: 2025/2/10 数据量太大的效率不高
        List<AlarmCenter> data = pageData.getData();
        for (AlarmCenter alarmCenter : data) {
            String alarmLevel = alarmCenter.getAlarmLevel();
            switch (alarmLevel) {
                case "1":
                    alarmCenter.setAlarmLevel("提醒报警");
                    break;
                case "2":
                    alarmCenter.setAlarmLevel("重要报警");
                    break;
                case "3":
                    alarmCenter.setAlarmLevel("紧急报警");
                    break;
            }
        }

        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

        return IstarResponse.ok();
    }

    /**
     * 查询登陆用户相关告警信息
     * @param request
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("own")
    public IstarResponse own(AlarmCenterListRequest request) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        request.setType("2");
        request.setUserId(userId);
        return IstarResponse.ok(alarmCenterService.findList(request, getTenantId()));
    }

    @GetMapping("findAll")
    public IstarResponse findAll(AlarmCenterListRequest request) throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.findAll(request, getTenantId()));
    }

    /**
     * 解除告警
     * @param ids
     * @return
     * @throws ThingsboardException
     */
    @PostMapping("clearAlarm")
    public IstarResponse clearAlarm(@RequestBody List<String> ids) throws ThingsboardException {
        alarmCenterService.clearAlarm(ids, getCurrentUser());
        return IstarResponse.ok();
    }

    /**
     * 创建告警工单
     * @param alarmId
     * @param request
     * @return
     */
    @PostMapping("createWorkOrder/{alarmId}")
    public IstarResponse createWorkOrder(@PathVariable String alarmId, @RequestBody WorkOrderSaveRequest request) {
        try {
            request.tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            alarmCenterService.createWorkOrder(alarmId, request);
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
        return IstarResponse.ok();
    }

    /**
     * 报警分析 - 优化版本
     * @param startTime
     * @param endTime
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("report")
    public IstarResponse report(@RequestParam Long startTime, @RequestParam Long endTime) throws ThingsboardException {
        // 查询报表数据 - 使用优化后的方法
        return IstarResponse.ok(alarmCenterService.findReport(startTime, endTime, getTenantId()));
    }

    /**
     * 报警分析 - 分页版本（如果前端需要详细数据列表）
     * @param startTime
     * @param endTime
     * @param page
     * @param size
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("report/paging")
    public IstarResponse reportWithPaging(@RequestParam Long startTime,
                                         @RequestParam Long endTime,
                                         @RequestParam(defaultValue = "1") int page,
                                         @RequestParam(defaultValue = "20") int size) throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.findReportWithPaging(startTime, endTime, getTenantId(), page, size));
    }

    /**
     * 根据站点排名
     * @param startTime
     * @param endTime
     * @param stationType
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("rankByStation")
    public IstarResponse rankByStation(@RequestParam Long startTime, @RequestParam Long endTime,
                                       @RequestParam(required = false) String stationType) throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.rankByStation(startTime, endTime, stationType, getTenantId()));
    }

    /**
     * 年度分析
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("yearReport")
    public IstarResponse yearReport() throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.yearReport(getTenantId()));
    }

    /**
     * 根据告警等级统计数量
     * @param startTime
     * @param endTime
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("countByLevel")
    public IstarResponse countByLevel(@RequestParam Long startTime, @RequestParam Long endTime) throws ThingsboardException {
        return IstarResponse.ok(alarmCenterService.countByLevel(startTime, endTime, getTenantId()));
    }

    /**
     * 报警总览
     * 报警总数，报警设备数，待确认数，待解除数
     */
    @RequestMapping(value = "/countAlarm/overview", method = RequestMethod.GET)
    @ResponseBody
    public Object countAlarm(@RequestParam long start, @RequestParam long end) throws Exception {
        AlarmCenterListRequest request = new AlarmCenterListRequest();
        request.setPage(1);
        request.setSize(Integer.MAX_VALUE);
        request.setAlarmStatus("1");

        PageData<AlarmCenter> alarmPageData = alarmCenterService.findList(request, getTenantId());
        List<AlarmCenter> alarmList = alarmPageData.getData();
        // 统计离线告警
        List<AlarmCenter> offlineAlarm = alarmList.stream().filter(alarm -> "离线告警".equals(alarm.getAlarmType())).collect(Collectors.toList());

        // 统计其他告警
        List<AlarmCenter> otherAlarm = alarmList.stream().filter(alarm -> !"离线告警".equals(alarm.getAlarmType())).collect(Collectors.toList());

        // 统计设备数
        List<String> deviceList = alarmList.stream()
                .map(AlarmCenter::getDeviceId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());


        JSONObject result = new JSONObject();
        result.put("alarmTotal", alarmList.size());
        result.put("alarmDeviceTotal", deviceList.size());
        result.put("offlineTotal", offlineAlarm.size());
        result.put("otherTotal", otherAlarm.size());

        return result;
    }

    /**
     * 告警排行
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("alarmRank")
    public Object alarmRank() throws ThingsboardException {

        AlarmCenterListRequest request = new AlarmCenterListRequest();
        List<AlarmCenter> alarms = alarmCenterService.findAll(request, getTenantId());

        // 查询项目
        List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
        Map<String, ProjectEntity> projectMap = projectList.stream()
                .collect(Collectors.toMap(ProjectEntity::getId, project -> project));

        // 查询站点
        List<StationEntity> stationList = stationService.findByTenantId(getTenantId());
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, s -> s));

        Map<String, List<AlarmCenter>> stationAlarmMap = new HashMap<>();
        if (alarms.size() > 0) {
            for (AlarmCenter alarm : alarms) {
                String stationId = alarm.getStationId();
                List<AlarmCenter> dataList = stationAlarmMap.get(stationId);
                if (dataList == null) {
                    dataList = new ArrayList<>();
                }
                dataList.add(alarm);
                stationAlarmMap.put(stationId, dataList);
            }
        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmCenter>> entry : stationAlarmMap.entrySet()) {
            String key = entry.getKey();
            List<AlarmCenter> value = entry.getValue();

            StationEntity station = stationMap.get(key);
            JSONObject result = new JSONObject();
            if (station != null) {
                result.put("deviecName", station.getName());
                ProjectEntity project = projectMap.get(station.getProjectId());
                if (project != null) {
                    result.put("projectName", project.getName());
                } else {
                    result.put("projectName", "");
                }

            } else {
                continue;
            }
            result.put("alarm", value == null ? 0 : value.size());

            resultList.add(result);
        }

        return resultList.stream().sorted((r1, r2) -> {
            Integer alarm1 = r1.getIntValue("alarm");
            Integer alarm2 = r2.getIntValue("alarm");
            return alarm2.compareTo(alarm1);
        }).collect(Collectors.toList());

    }

    /**
     * 告警分析，会查询全量内容，数据量大时，需要优化
     * @param request
     * @return
     * @throws ThingsboardException
     */
    @PostMapping("alarmAnalysis")
    public IstarResponse alarmAnalysis(@RequestBody AlarmCenterListRequest request) throws ThingsboardException {
        String analysisType = request.getAnalysisType();
        JSONObject param = new JSONObject();
        if ("1".equals(analysisType)) {
            String areaId = request.getAreaId();
            // 查询区域下的站点列表
            param.put("projectId", areaId);
        }
        if ("2".equals(analysisType)) {
            param.put("type", request.getStationType());
        }

        PageData<StationEntity> stationPageData = stationService.list(1, Integer.MAX_VALUE, param, getTenantId());
        List<StationEntity> stationList = stationPageData.getData();
        if ("3".equals(analysisType)) {
            stationList = stationList.stream().filter(station -> request.getStationId().equals(station.getId())).collect(Collectors.toList());
        }
        if (stationList == null || stationList.isEmpty()) {
            // 初始化数据返回
            return IstarResponse.ok(initNullData());
        }
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, s -> s));

        // 查询报警列表
        request.setPage(1);
        request.setSize(Integer.MAX_VALUE);
        request.setStationIdList(stationList.stream().map(StationEntity::getId).collect(Collectors.toList()));
        PageData<AlarmCenter> alarmPageData = alarmCenterService.findList(request, getTenantId());
        List<AlarmCenter> alarmList = alarmPageData.getData();
        if (alarmList == null || alarmList.isEmpty()) {
            // 初始化数据返回
            return IstarResponse.ok(initNullData());
        }

        // 统计数据类型
        Map<String, List<AlarmCenter>> groupMap = new HashMap<>();
        for (AlarmCenter alarmCenter : alarmList) {
            List<AlarmCenter> list = new ArrayList<>();
            String key = "";
            if ("1".equals(request.getQueryType())) {// 告警类型
                key = alarmCenter.getAlarmType();
            }
            if ("2".equals(request.getQueryType())) {// 告警等级
                String alarmLevel = alarmCenter.getAlarmLevel();
                switch (alarmLevel) {
                    case "1":
                        key = "提醒告警";
                        break;
                    case "2":
                        key = "重要告警";
                        break;
                    case "3":
                        key = "紧急告警";
                        break;
                }
            }
            if ("3".equals(request.getQueryType())) {// 站点类型
                String stationId = alarmCenter.getStationId();
                StationEntity station = stationMap.get(stationId);
                if (station != null) {
                    key = station.getType();
                }
            }
            // 数据分组
            if (StringUtils.isNotBlank(key)) {
                if (groupMap.containsKey(key)) {
                    list = groupMap.get(key);
                }
                list.add(alarmCenter);
                groupMap.put(key, list);
            }
        }
        // 数据统计(总量、告警排行、告警统计、告警占比、按类型以及处理状态列表)
        JSONObject result = new JSONObject();
        // 总量、概览
        JSONObject totalCount = new JSONObject();
        int total = alarmList.size();
        List<AlarmCenter> processedList = alarmList.stream()
                .filter(alarm -> DataConstants.ALARMV2_PROCESS_STATUS.COMPLETE.getValue().equals(alarm.getProcessStatus()))
                .collect(Collectors.toList());
        int processed = processedList.size();
        int unProcess = total - processed;
        // 工单处理
        long orderProcess = processedList.stream().filter(alarm -> StringUtils.isNotBlank(alarm.getWorkOrderId())).count();
        // 快速处理
        long otherProcess = processed - orderProcess;
        totalCount.put("total", total);
        totalCount.put("processed", processed);
        totalCount.put("unProcess", unProcess);
        totalCount.put("orderProcess", orderProcess);
        totalCount.put("otherProcess", otherProcess);

        // 告警排行/告警统计/告警占比
        List<JSONObject> rankList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmCenter>> entry : groupMap.entrySet()) {
            String key = entry.getKey();
            List<AlarmCenter> value = entry.getValue();
            JSONObject rankData = new JSONObject();
            rankData.put("name", key);
            if (value != null) {
                rankData.put("count", value.size());
            } else {
                rankData.put("count", 0);
            }
            rankList.add(rankData);
        }
        // 排序
        rankList.sort((o1, o2) -> {
            Long count = o1.getLong("count");
            Long count1 = o2.getLong("count");
            return count1.compareTo(count);
        });

        // 统计列表
        List<JSONObject> statusCountList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmCenter>> entry : groupMap.entrySet()) {
            String key = entry.getKey();
            List<AlarmCenter> value = entry.getValue();
            JSONObject processCount = new JSONObject();
            JSONObject unProcessCount = new JSONObject();
            processCount.put("name", key);
            processCount.put("status", "已处理");
            unProcessCount.put("name", key);
            unProcessCount.put("status", "未处理");
            if (value != null) {
                int size = value.size();
                long count = value.stream().filter(alarm -> DataConstants.ALARMV2_PROCESS_STATUS.COMPLETE.getValue().equals(alarm.getProcessStatus())).count();
                processCount.put("count", count);
                unProcessCount.put("count", size - count);

            } else {
                processCount.put("count", 0);
                unProcessCount.put("count", 0);
            }
            statusCountList.add(processCount);
            statusCountList.add(unProcessCount);
        }

        result.put("totalCount", totalCount);
        result.put("rankList", rankList);
        result.put("statusCountList", statusCountList);

        return IstarResponse.ok(result);
    }

    private JSONObject initNullData() {
        JSONObject result = new JSONObject();
        JSONObject totalCount = new JSONObject();
        totalCount.put("total", 0);
        totalCount.put("processed", 0);
        totalCount.put("unProcess", 0);
        totalCount.put("orderProcess", 0);
        totalCount.put("otherProcess", 0);
        result.put("totalCount", totalCount);

        List<JSONObject> rankList = new ArrayList<>();
        rankList.add(JSONObject.parseObject("{\"name\":\"液位异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"水质异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备故障\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"通讯异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"流量异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"控制异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备健康\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"机组电压异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"压力异常\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"其他\", \"count\":0}"));
        result.put("rankList", rankList);

        List<JSONObject> statusCountList = new ArrayList<>();
        rankList.add(JSONObject.parseObject("{\"name\":\"液位异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"液位异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"水质异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"水质异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备故障\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备故障\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"通讯异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"通讯异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"流量异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"流量异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"控制异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"控制异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备健康\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"设备健康\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"机组电压异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"机组电压异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"压力异常\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"压力异常\", \"status\":\"未处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"其他\", \"status\":\"已处理\", \"count\":0}"));
        rankList.add(JSONObject.parseObject("{\"name\":\"其他\", \"status\":\"未处理\", \"count\":0}"));
        result.put("statusCountList", statusCountList);

        return result;
    }


}
