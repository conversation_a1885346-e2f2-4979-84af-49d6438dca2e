<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.alarmV2.AlarmCenterMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter">
        SELECT
        A.*,coalesce(s.name, A.station_id) AS "stationName",s.type AS "stationType", coalesce(b.station_attr_id, tsa2.id) AS "stationAttrId", coalesce(tsa.attr, tsa2.attr) as "attr" ,coalesce(tsa.name, tsa2.name) AS "attrName", d.name AS "deviceName"
        FROM
        tb_alarm_center A
            LEFT JOIN tb_station s ON a.station_id = s.id
            LEFT JOIN tb_alarm_rule b ON a.alarm_rule_id = b.id
            LEFT JOIN tb_station_attr tsa ON b.station_attr_id = tsa.id
            LEFT JOIN device d ON d.id = a.device_id
            left join tb_alarm_rule_smart tars on a.alarm_rule_id = tars.id
            left join tb_station_attr tsa2 on tars.station_id = tsa2.station_id and tars.attr = tsa2.attr
        <if test="param.userId != null and param.userId != ''">
            left join tb_alarm_rule tar on A.alarm_rule_id = tar.id
            left join tb_alarm_rule_user taru on tar.id = taru.main_id
        </if>
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.alarmType != null and param.alarmType != ''">
                AND a.alarm_type = #{param.alarmType}
            </if>
            <if test="param.alarmLevel != null and param.alarmLevel != ''">
                AND a.alarm_level = #{param.alarmLevel}
            </if>
            <if test="param.processStatus != null and param.processStatus != ''">
                AND a.process_status = #{param.processStatus}
            </if>
            <if test="param.alarmStatus != null and param.alarmStatus != ''">
                AND a.alarm_status = #{param.alarmStatus}
            </if>
            <if test="param.startTime != null">
                AND a.time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND a.time &lt;= #{param.endTime}
            </if>
            <if test="param.userId != null and param.userId != ''">
                AND taru.user_id = #{param.userId}
                <if test="param.type != null and param.type != ''">
                    AND taru.type = #{param.type}
                </if>
            </if>
            <if test="param.stationIdList != null and param.stationIdList.size() > 0">
                AND a.station_id IN
                <foreach collection="param.stationIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.stationTypeList != null and param.stationTypeList.size() > 0">
                AND s.type IN
                <foreach collection="param.stationTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.time DESC
    </select>

    <select id="findAll" resultType="org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter">
        SELECT
        A.*,s.name AS "stationName",s.type AS "stationType",b.station_attr_id AS "stationAttrId"
        FROM
        tb_alarm_center A LEFT JOIN tb_station s ON a.station_id = s.id LEFT JOIN tb_alarm_rule b ON a.alarm_rule_id = b.id
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.alarmType != null and param.alarmType != ''">
                AND a.alarm_type = #{param.alarmType}
            </if>
            <if test="param.alarmLevel != null and param.alarmLevel != ''">
                AND a.alarm_level = #{param.alarmLevel}
            </if>
            <if test="param.processStatus != null and param.processStatus != ''">
                AND a.process_status = #{param.processStatus}
            </if>
            <if test="param.alarmStatus != null and param.alarmStatus != ''">
                AND a.alarm_status = #{param.alarmStatus}
            </if>
            <if test="param.stationIdList != null and param.stationIdList.size() > 0">
                AND a.station_id IN
                <foreach collection="param.stationIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.time DESC
    </select>

    <select id="findByAlarm" resultType="org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter">
        SELECT
        A.*,s.name AS "stationName",s.type AS "stationType"
        FROM
        tb_alarm_center A LEFT JOIN tb_station s ON a.station_id = s.id
        <where>
            s.id IS NOT NULL
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="start != null and end != null">
                AND a.time BETWEEN #{start} AND #{end}
            </if>
        </where>
        ORDER BY a.time DESC
    </select>

    <!-- 优化：按站点类型分组统计 -->
    <select id="findAlarmGroupByStationType" resultType="java.util.Map">
        SELECT
            s.type AS stationType,
            COUNT(*) AS count,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', A.id,
                    'title', A.title,
                    'time', A.time,
                    'alarmLevel', A.alarm_level,
                    'alarmStatus', A.alarm_status,
                    'stationName', s.name
                )
            ) AS alarmList
        FROM tb_alarm_center A
        LEFT JOIN tb_station s ON a.station_id = s.id
        <where>
            s.id IS NOT NULL
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="start != null and end != null">
                AND a.time BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY s.type
        ORDER BY count DESC
    </select>

    <!-- 优化：按报警级别分组统计 -->
    <select id="findAlarmGroupByLevel" resultType="java.util.Map">
        SELECT
            A.alarm_level AS alarmLevel,
            COUNT(*) AS count,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', A.id,
                    'title', A.title,
                    'time', A.time,
                    'alarmLevel', A.alarm_level,
                    'alarmStatus', A.alarm_status,
                    'stationName', s.name,
                    'stationType', s.type
                )
            ) AS alarmList
        FROM tb_alarm_center A
        LEFT JOIN tb_station s ON a.station_id = s.id
        <where>
            s.id IS NOT NULL
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="start != null and end != null">
                AND a.time BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY A.alarm_level
        ORDER BY count DESC
    </select>

    <select id="rankByStation" resultType="org.thingsboard.server.dao.model.DTO.CountObjDTO">
        select a.station_id AS "key", count(*) AS "count"
        FROM tb_alarm_center a LEFT JOIN tb_station b ON a.station_id = b.id
        <where>
            b.id IS NOT NULL
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="start != null and end != null">
                AND a.time BETWEEN #{start} AND #{end}
            </if>
            <if test="stationType != null and stationType != ''">
                AND b.type = #{stationType}
            </if>
        </where>
        GROUP BY a.station_id
        ORDER BY count DESC
    </select>
    <select id="lastAlarmByAlarmRuleId" resultType="org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter">
        SELECT * FROM tb_alarm_center WHERE alarm_rule_id = #{id} ORDER BY time DESC LIMIT 1
    </select>

</mapper>