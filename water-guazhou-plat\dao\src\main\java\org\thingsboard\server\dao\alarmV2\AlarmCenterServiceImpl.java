package org.thingsboard.server.dao.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.app.AppMsgPushService;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.request.AlarmCenterListRequest;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.notify.SystemNotifyService;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.alarmV2.AlarmCenterMapper;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.ExecutorUtil;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.ALARM_CACHE;

@Slf4j
@Service
public class AlarmCenterServiceImpl implements AlarmCenterService {

    @Autowired
    private AlarmCenterMapper alarmCenterMapper;

    @Autowired
    private NewlyWorkOrderService workOrderService;

    @Autowired
    private StationService stationService;

    @Autowired
    private UserService userService;

    @Autowired
    private AppMsgPushService appMsgPushService;

    @Autowired
    private SystemNotifyService systemNotifyService;

    @Override
//    @CacheEvict(cacheNames = ALARM_CACHE, key = "{#alarmCenter.alarmRuleId}")
    public void save(AlarmCenter alarmCenter) {
        if (StringUtils.isNotBlank(alarmCenter.getId())) {
            alarmCenterMapper.updateById(alarmCenter);
        } else {
            alarmCenterMapper.insert(alarmCenter);
        }

        ExecutorUtil.getExecutorService().submit(() -> {
            List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(alarmCenter.getTenantId())));
            if (userList != null) {
                for (User user : userList) {
                    systemNotifyService.sendNotify(
                            DataConstants.SYSTEM_NOTIFY_TYPE.ALARM.getValue(),
                            DataConstants.SYSTEM_NOTIFY_TOPIC.ALARM_CENTER,
                            "系统告警",
                            UUIDConverter.fromTimeUUID(user.getUuidId()),
                            alarmCenter.getTenantId()
                    );
                    try {
                        appMsgPushService.pushMsgToUser(UUIDConverter.fromTimeUUID(user.getUuidId()), "报警", alarmCenter.getAlarmInfo());
                    } catch (Exception e) {
                        log.error("发送通知到用户手机失败, 失败原因: ", e);
                    }
                }
            }
        });

    }

    @Override
    public PageData<AlarmCenter> findList(AlarmCenterListRequest request, TenantId tenantId) {
        Page<AlarmCenter> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        if (StringUtils.isNotBlank(request.getStationId())) {
            request.setStationIdList(Arrays.asList(request.getStationId().split(",")));
        }
        if (StringUtils.isNotBlank(request.getStationType())) {
            request.setStationTypeList(Arrays.asList(request.getStationType().split(",")));
        }
        IPage<AlarmCenter> pageResult = alarmCenterMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void clearAlarm(List<String> ids, User currentUser) {
        QueryWrapper<AlarmCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        List<AlarmCenter> alarmCenters = alarmCenterMapper.selectList(queryWrapper);
        if (alarmCenters == null || alarmCenters.isEmpty()) {
            return;
        }
        for (AlarmCenter alarmCenter : alarmCenters) {
            if ("1".equals(alarmCenter.getAlarmStatus())) {
                alarmCenter.setOptionUser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
                alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.CLEARED.getValue());// 已解除
                alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.COMPLETE.getValue());// 已处理
                alarmCenter.setEndTime(new Date());
                alarmCenterMapper.updateById(alarmCenter);
            }
        }
    }

    @Override
    @Transactional
    public void createWorkOrder(String alarmId, WorkOrderSaveRequest request) throws ThingsboardException {
        AlarmCenter alarmCenter = alarmCenterMapper.selectById(alarmId);
        if (alarmCenter == null) {
            throw new ThingsboardException("要生成工单的报警不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 创建工单
        WorkOrder workOrder = workOrderService.save(request);
        alarmCenter.setWorkOrderId(workOrder.getId());
        alarmCenterMapper.updateById(alarmCenter);

    }

    @Override
    public void complete(String orderId) {
        QueryWrapper<AlarmCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("work_order_id", orderId);
        List<AlarmCenter> alarmCenterList = alarmCenterMapper.selectList(queryWrapper);
        if (alarmCenterList == null || alarmCenterList.isEmpty()) {
            return;
        }
        for (AlarmCenter alarmCenter : alarmCenterList) {
            alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.COMPLETE.getValue());
            alarmCenter.setEndTime(new Date());
            alarmCenterMapper.updateById(alarmCenter);
        }

    }

    @Override
//    @Cacheable(cacheNames = ALARM_CACHE, key = "{#alarmRuleId}")
    public Integer countAlarmByAlarmRuleId(String alarmRuleId) {
        QueryWrapper<AlarmCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alarm_rule_id", alarmRuleId).eq("alarm_status", "1");
        return alarmCenterMapper.selectCount(queryWrapper);
    }

    @Override
    public List<AlarmCenter> findAlarmByAlarmRuleId(String alarmRuleId) {
        QueryWrapper<AlarmCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alarm_rule_id", alarmRuleId).eq("alarm_status", "3").orderByDesc("time");
        return alarmCenterMapper.selectList(queryWrapper);
    }

    @Override
    // 添加缓存，缓存时间5分钟（对于报表数据可以接受短时间的延迟）
    // @Cacheable(cacheNames = "alarm_report", key = "#startTime + '_' + #endTime + '_' + #tenantId.id", unless = "#result == null")
    public Object findReport(Long startTime, Long endTime, TenantId tenantId) {
        long startQueryTime = System.currentTimeMillis();

        // 查询全量数据
        List<AlarmCenter> alarmCenterList = alarmCenterMapper.findByAlarm(
            new Date(startTime),
            new Date(endTime),
            UUIDConverter.fromTimeUUID(tenantId.getId())
        );

        long endQueryTime = System.currentTimeMillis();

        // 优化：使用Stream API和Collectors进行高效分组
        Map<String, List<AlarmCenter>> alarmMap = alarmCenterList.stream()
            .filter(alarm -> alarm.getStationType() != null) // 过滤空值
            .collect(Collectors.groupingBy(AlarmCenter::getStationType));

        Map<String, List<AlarmCenter>> alarmLevelMap = alarmCenterList.stream()
            .filter(alarm -> alarm.getAlarmLevel() != null) // 过滤空值
            .collect(Collectors.groupingBy(AlarmCenter::getAlarmLevel));

        long groupingEndTime = System.currentTimeMillis();

        // 构建返回结果
        JSONObject result = new JSONObject();
        result.put("total", alarmCenterList.size());

        // 转换站点类型数据为前端需要的格式
        List<JSONObject> alarmDataList = alarmMap.entrySet().stream()
            .map(entry -> {
                JSONObject alarmDataObj = new JSONObject();
                alarmDataObj.put("title", entry.getKey());
                alarmDataObj.put("list", entry.getValue());
                return alarmDataObj;
            })
            .collect(Collectors.toList());

        result.put("alarmData", alarmDataList);
        result.put("alarmLevelData", alarmLevelMap);

        long finishQueryTime = System.currentTimeMillis();
        log.info("数据库查询耗时: {}ms", endQueryTime - startQueryTime);
        log.info("数据分组耗时: {}ms", groupingEndTime - endQueryTime);
        log.info("结果构建耗时: {}ms", finishQueryTime - groupingEndTime);
        log.info("总处理耗时: {}ms", finishQueryTime - startQueryTime);
        log.info("数据总量: {}", alarmCenterList.size());

        return result;
    }

    @Override
    public CompletableFuture<Object> findReportAsync(Long startTime, Long endTime, TenantId tenantId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return findReport(startTime, endTime, tenantId);
            } catch (Exception e) {
                log.error("异步查询报警报表失败", e);
                throw new RuntimeException("异步查询报警报表失败", e);
            }
        }, ExecutorUtil.getExecutor());
    }

    @Override
    public Object findReportWithPaging(Long startTime, Long endTime, TenantId tenantId, int page, int size) {
        // 如果需要分页版本，可以在这里实现
        // 目前先返回全量数据
        return findReport(startTime, endTime, tenantId);
    }

    @Override
    public List<CountObjDTO> rankByStation(Long startTime, Long endTime, String stationType, TenantId tenantId) {
        List<CountObjDTO> countList = alarmCenterMapper.rankByStation(new Date(startTime), new Date(endTime), UUIDConverter.fromTimeUUID(tenantId.getId()), stationType);
        List<StationEntity> stationList = stationService.findByTenantId(tenantId);
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        List<CountObjDTO> resultList = new ArrayList<>();
        for (CountObjDTO countObj : countList) {
            StationEntity station = stationMap.get(countObj.getKey());
            if (station == null) {
                continue;
            }
            countObj.setKey(station.getName());
            resultList.add(countObj);
        }

        return resultList;
    }

    @Override
    public List<JSONObject> yearReport(TenantId tenantId) {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, instance.getActualMinimum(Calendar.MONTH));
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date yearStart = instance.getTime();
        Date yearEnd = new Date();

        List<AlarmCenter> alarmList = alarmCenterMapper.findByAlarm(yearStart, yearEnd, UUIDConverter.fromTimeUUID(tenantId.getId()));
        // 将数据按每个月分组
        Map<String, List<AlarmCenter>> monthAlarmMap = new LinkedHashMap<>();
        // 初始化Map
        for (int i = 1; i <= 12; i++) {
            monthAlarmMap.put(instance.get(Calendar.YEAR) + "-" + String.format("%02d", i), new ArrayList<>());
        }
        // 进行数据分组
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        for (AlarmCenter alarm : alarmList) {
            Date time = alarm.getTime();
            String format = dateFormat.format(time);
            List<AlarmCenter> monthAlarmList = monthAlarmMap.get(format);
            monthAlarmList.add(alarm);
        }

        // 统计每个月的报警数据, 包含每月报警数、已处理数、未处理数、处理率
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmCenter>> entry : monthAlarmMap.entrySet()) {
            String key = entry.getKey();
            List<AlarmCenter> value = entry.getValue();
            int total = 0;
            int end = 0;
            int unProcess = 0;
            BigDecimal processRate = BigDecimal.ZERO;
            if (value != null) {
                for (AlarmCenter alarmCenter : value) {
                    total++;
                    if (!DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue().equals(alarmCenter.getAlarmStatus())) {
                        end++;
                    } else {
                        unProcess++;
                    }
                }
            }
            if (total != 0) {
                // 计算处理率
                processRate = BigDecimal.valueOf(end).divide(BigDecimal.valueOf(total), 4).multiply(new BigDecimal("100"));
            }
            JSONObject data = new JSONObject();
            data.put("month", key);
            data.put("total", total);
            data.put("end", end);
            data.put("unProcess", unProcess);
            data.put("processRate", processRate);

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public JSONObject countByLevel(Long startTime, Long endTime, TenantId tenantId) {
        List<AlarmCenter> alarmList = alarmCenterMapper.findByAlarm(new Date(startTime), new Date(endTime), UUIDConverter.fromTimeUUID(tenantId.getId()));
        int level1 = 0;
        int level2 = 0;
        int level3 = 0;
        for (AlarmCenter alarm : alarmList) {
            if ("1".equals(alarm.getAlarmLevel())) {
                level1++;
            }
            if ("2".equals(alarm.getAlarmLevel())) {
                level2++;
            }
            if ("3".equals(alarm.getAlarmLevel())) {
                level3++;
            }
        }
        JSONObject result = new JSONObject();
        result.put("level1", level1);
        result.put("level2", level2);
        result.put("level3", level3);

        return result;
    }

    @Override
    public List<AlarmCenter> findAll(AlarmCenterListRequest request, TenantId tenantId) {
        if (StringUtils.isNotBlank(request.getStationId())) {
            request.setStationIdList(Arrays.asList(request.getStationId().split(",")));
        }
        return alarmCenterMapper.findAll(request);
    }

    @Override
    @CacheEvict(cacheNames = ALARM_CACHE, key = "{#alarmRuleId}")
    public void restoreAlarm(String alarmRuleId) {
        QueryWrapper<AlarmCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alarm_rule_id", alarmRuleId).eq("alarm_status", "1");

        AlarmCenter alarmCenter = new AlarmCenter();
        alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.RESTORE.getValue());// 已恢复
        alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.COMPLETE.getValue());
        alarmCenter.setEndTime(new Date());
        alarmCenterMapper.update(alarmCenter, queryWrapper);
    }

    @Override
    public AlarmCenter lastAlarmByAlarmRuleId(String id) {
        return alarmCenterMapper.lastAlarmByAlarmRuleId(id);
    }
}
