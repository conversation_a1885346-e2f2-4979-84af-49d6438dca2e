-- 报警中心表性能优化索引
-- 针对 findReport 接口的查询优化

-- 1. 复合索引：tenant_id + time（用于时间范围查询）
CREATE INDEX IF NOT EXISTS idx_alarm_center_tenant_time 
ON tb_alarm_center (tenant_id, time);

-- 2. 复合索引：tenant_id + time + alarm_level（用于按级别分组）
CREATE INDEX IF NOT EXISTS idx_alarm_center_tenant_time_level 
ON tb_alarm_center (tenant_id, time, alarm_level);

-- 3. 站点表索引优化（如果不存在）
CREATE INDEX IF NOT EXISTS idx_station_id_type 
ON tb_station (id, type);

-- 4. 分析表统计信息（PostgreSQL）
ANALYZE tb_alarm_center;
ANALYZE tb_station;

-- 查询执行计划分析（用于验证索引效果）
-- EXPLAIN ANALYZE 
-- SELECT A.*, s.name AS "stationName", s.type AS "stationType"
-- FROM tb_alarm_center A 
-- LEFT JOIN tb_station s ON a.station_id = s.id
-- WHERE s.id IS NOT NULL 
--   AND a.tenant_id = 'your-tenant-id'
--   AND a.time BETWEEN '2024-01-01' AND '2024-12-31'
-- ORDER BY a.time DESC;
