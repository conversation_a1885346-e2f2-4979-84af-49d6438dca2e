package org.thingsboard.server.dao.alarmV2;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.request.AlarmCenterListRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.util.List;
public interface AlarmCenterService {

    void save(AlarmCenter alarmCenter);

    PageData<AlarmCenter> findList(AlarmCenterListRequest request, TenantId tenantId);

    void clearAlarm(List<String> ids, User currentUser);

    void createWorkOrder(String alarmId, WorkOrderSaveRequest request) throws ThingsboardException;

    /**
     * 报警处理完成
     * @param id 工单id
     */
    void complete(String orderId);

    Integer countAlarmByAlarmRuleId(String alarmRuleId);

    List<AlarmCenter> findAlarmByAlarmRuleId(String alarmRuleId);

    Object findReport(Long startTime, Long endTime, TenantId tenantId);

    List<CountObjDTO> rankByStation(Long startTime, Long endTime, String stationType, TenantId tenantId);

    List<JSONObject> yearReport(TenantId tenantId);

    JSONObject countByLevel(Long startTime, Long endTime, TenantId tenantId);

    List<AlarmCenter> findAll(AlarmCenterListRequest request, TenantId tenantId);

    void restoreAlarm(String alarmRuleId);

    AlarmCenter lastAlarmByAlarmRuleId(String id);
}
